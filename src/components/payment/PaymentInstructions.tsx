'use client'
import React, { useState } from 'react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    HStack,
    Badge,
    Flex,
    Icon,
    Separator,
    Code,
    Alert,
    AlertDescription,
    Image,
    Grid,
    GridItem,
    Skeleton,
} from '@chakra-ui/react'
import {
    FaCreditCard,
    FaUniversity,
    FaMobileAlt,
    FaQrcode,
    FaCopy,
    FaExternalLinkAlt,
    FaClock,
    FaCheckCircle,
    FaTimesCircle,
    FaSpinner,
    FaStore,
} from 'react-icons/fa'
import { useTranslations } from 'next-intl'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { toaster } from '@/components/ui/toaster'

interface PaymentInstructionsProps {
    payment: any
    paymentInstructions: any
    isLoading?: boolean
}

const PaymentInstructions: React.FC<PaymentInstructionsProps> = ({
    payment,
    paymentInstructions,
    isLoading = false
}) => {
    const t = useTranslations()
    const { formatPrice } = useCurrencyLanguage()
    const [copiedField, setCopiedField] = useState<string | null>(null)

    const copyToClipboard = async (text: string, fieldName: string) => {
        try {
            await navigator.clipboard.writeText(text)
            setCopiedField(fieldName)
            toaster.create({
                title: "Copied!",
                description: `${fieldName} copied to clipboard`,
                type: "success",
            })
            setTimeout(() => setCopiedField(null), 2000)
        } catch (error) {
            toaster.create({
                title: "Copy Failed",
                description: "Failed to copy to clipboard",
                type: "error",
            })
        }
    }

    const getStatusIcon = (status: string) => {
        switch (status.toUpperCase()) {
            case 'PAID': return FaCheckCircle
            case 'PENDING': return FaClock
            case 'EXPIRED': return FaTimesCircle
            case 'FAILED': return FaTimesCircle
            default: return FaSpinner
        }
    }

    const getStatusColor = (status: string) => {
        switch (status.toUpperCase()) {
            case 'PAID': return 'green'
            case 'PENDING': return 'orange'
            case 'EXPIRED': return 'red'
            case 'FAILED': return 'red'
            default: return 'gray'
        }
    }

    if (isLoading) {
        return (
            <VStack gap={4} align="stretch">
                <Skeleton height="60px" />
                <Skeleton height="200px" />
                <Skeleton height="150px" />
            </VStack>
        )
    }

    if (!payment || !paymentInstructions) {
        return (
            <Alert.Root status="warning">
                <AlertDescription>
                    Payment information not available
                </AlertDescription>
            </Alert.Root>
        )
    }

    const renderPaymentMethodIcon = (method: string) => {
        switch (method) {
            case 'xendit_invoice': return FaCreditCard
            case 'credit_card': return FaCreditCard
            case 'bank_transfer': return FaUniversity
            case 'ewallet': return FaMobileAlt
            default: return FaCreditCard
        }
    }

    const renderBankTransferInstructions = () => (
        <VStack gap={4} align="stretch">
            <Heading size="md">Bank Transfer Instructions</Heading>
            <Alert.Root status="info">
                <AlertDescription>
                    Transfer the exact amount to the virtual account number below
                </AlertDescription>
            </Alert.Root>
            
            {paymentInstructions.bankCode && (
                <Box p={4} bg="gray.50" borderRadius="md">
                    <VStack gap={3} align="stretch">
                        <HStack justify="space-between">
                            <Text fontWeight="medium">Bank:</Text>
                            <Text>{paymentInstructions.bankName}</Text>
                        </HStack>
                        <HStack justify="space-between">
                            <Text fontWeight="medium">Account Number:</Text>
                            <HStack>
                                <Code fontSize="lg" p={2}>{paymentInstructions.accountNumber}</Code>
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => copyToClipboard(paymentInstructions.accountNumber, 'Account Number')}
                                >
                                    <Icon as={FaCopy} />
                                </Button>
                            </HStack>
                        </HStack>
                        <HStack justify="space-between">
                            <Text fontWeight="medium">Amount:</Text>
                            <Text fontWeight="bold" fontSize="lg">
                                {formatPrice(payment.amount, payment.currency)}
                            </Text>
                        </HStack>
                    </VStack>
                </Box>
            )}
        </VStack>
    )

    const renderEWalletInstructions = () => (
        <VStack gap={4} align="stretch">
            <Heading size="md">E-Wallet Payment</Heading>
            <Alert.Root status="info">
                <AlertDescription>
                    Complete payment using your {paymentInstructions.ewalletType} app
                </AlertDescription>
            </Alert.Root>

            {paymentInstructions.qrString && (
                <Box p={4} bg="gray.50" borderRadius="md" textAlign="center">
                    <VStack gap={3}>
                        <Icon as={FaQrcode} boxSize={8} color="blue.500" />
                        <Text fontWeight="medium">Scan QR Code</Text>
                        <Text fontSize="sm" color="gray.600">
                            Open your {paymentInstructions.ewalletType} app and scan the QR code
                        </Text>
                    </VStack>
                </Box>
            )}

            {paymentInstructions.mobileDeeplink && (
                <Button
                    colorScheme="blue"
                    size="lg"
                    // leftIcon={<Icon as={FaMobileAlt} />}
                    // rightIcon={<Icon as={FaExternalLinkAlt} />}
                    onClick={() => window.open(paymentInstructions.mobileDeeplink, '_blank')}
                >
                    Open {paymentInstructions.ewalletType} App
                </Button>
            )}
        </VStack>
    )

    const renderInvoiceInstructions = () => (
        <VStack gap={4} align="stretch">
            <Heading size="md">Payment Options</Heading>
            <Alert.Root status="info">
                <AlertDescription>
                    Multiple payment methods available. Click the button below to choose your preferred method.
                </AlertDescription>
            </Alert.Root>

            {paymentInstructions.invoiceUrl && (
                <Button
                    colorScheme="blue"
                    size="lg"
                    // leftIcon={<Icon as={FaCreditCard} />}
                    // rightIcon={<Icon as={FaExternalLinkAlt} />}
                    onClick={() => window.open(paymentInstructions.invoiceUrl, '_blank')}
                >
                    Complete Payment
                </Button>
            )}

            {/* Available payment methods */}
            <Box p={4} bg="gray.50" borderRadius="md">
                <Text fontWeight="medium" mb={3}>Available Payment Methods:</Text>
                <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={3}>
                    {paymentInstructions.availableChannels?.length > 0 && (
                        <GridItem>
                            <VStack gap={2}>
                                <Icon as={FaUniversity} color="blue.500" />
                                <Text fontSize="sm" fontWeight="medium">Bank Transfer</Text>
                                <Text fontSize="xs" color="gray.600">
                                    {paymentInstructions.availableChannels.join(', ')}
                                </Text>
                            </VStack>
                        </GridItem>
                    )}
                    
                    {paymentInstructions.availableEwallets?.length > 0 && (
                        <GridItem>
                            <VStack gap={2}>
                                <Icon as={FaMobileAlt} color="green.500" />
                                <Text fontSize="sm" fontWeight="medium">E-Wallets</Text>
                                <Text fontSize="xs" color="gray.600">
                                    {paymentInstructions.availableEwallets.join(', ')}
                                </Text>
                            </VStack>
                        </GridItem>
                    )}
                    
                    {paymentInstructions.availableRetailOutlets?.length > 0 && (
                        <GridItem>
                            <VStack gap={2}>
                                <Icon as={FaStore} color="purple.500" />
                                <Text fontSize="sm" fontWeight="medium">Retail Outlets</Text>
                                <Text fontSize="xs" color="gray.600">
                                    {paymentInstructions.availableRetailOutlets.join(', ')}
                                </Text>
                            </VStack>
                        </GridItem>
                    )}
                </Grid>
            </Box>
        </VStack>
    )

    const renderPaymentInstructions = () => {
        switch (paymentInstructions.method) {
            case 'bank_transfer':
                return renderBankTransferInstructions()
            case 'ewallet':
                return renderEWalletInstructions()
            case 'xendit_invoice':
                return renderInvoiceInstructions()
            default:
                return (
                    <Alert.Root status="info">
                        <AlertDescription>
                            Payment instructions will be available once payment is processed.
                        </AlertDescription>
                    </Alert.Root>
                )
        }
    }

    return (
        <VStack gap={6} align="stretch">
            {/* Payment Status */}
            <Box p={4} bg="white" borderRadius="lg" border="1px" borderColor="gray.200">
                <VStack gap={4} align="stretch">
                    <HStack justify="space-between" align="center">
                        <HStack gap={3}>
                            <Icon as={renderPaymentMethodIcon(paymentInstructions.method)} boxSize={6} color="blue.500" />
                            <Heading size="md">Payment Status</Heading>
                        </HStack>
                        <Badge 
                            colorScheme={getStatusColor(paymentInstructions.status)} 
                            variant="solid" 
                            size="lg"
                        >
                            <HStack gap={2}>
                                <Icon as={getStatusIcon(paymentInstructions.status)} boxSize={4} />
                                <Text textTransform="capitalize">{paymentInstructions.status}</Text>
                            </HStack>
                        </Badge>
                    </HStack>
                    
                    <Separator />
                    
                    <HStack justify="space-between">
                        <Text color="gray.600">Amount:</Text>
                        <Text fontWeight="bold" fontSize="lg">
                            {formatPrice(paymentInstructions.amount, paymentInstructions.currency)}
                        </Text>
                    </HStack>
                    
                    {paymentInstructions.expiryDate && paymentInstructions.status === 'PENDING' && (
                        <HStack justify="space-between">
                            <Text color="gray.600">Expires:</Text>
                            <Text color="orange.600" fontWeight="medium">
                                {new Date(paymentInstructions.expiryDate).toLocaleString()}
                            </Text>
                        </HStack>
                    )}
                </VStack>
            </Box>

            {/* Payment Instructions */}
            {paymentInstructions.status === 'PENDING' && (
                <Box p={4} bg="white" borderRadius="lg" border="1px" borderColor="gray.200">
                    {renderPaymentInstructions()}
                </Box>
            )}

            {/* Payment Completed */}
            {paymentInstructions.status === 'PAID' && (
                <Alert.Root status="success">
                    <AlertDescription>
                        Payment completed successfully! Your order is being processed.
                    </AlertDescription>
                </Alert.Root>
            )}

            {/* Payment Expired/Failed */}
            {(paymentInstructions.status === 'EXPIRED' || paymentInstructions.status === 'FAILED') && (
                <Alert.Root status="error">
                    <AlertDescription>
                        Payment {paymentInstructions.status.toLowerCase()}. Please create a new payment or contact support.
                    </AlertDescription>
                </Alert.Root>
            )}
        </VStack>
    )
}

export default PaymentInstructions
