'use client'
import React from 'react'
import {
  Box,
  Card,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Grid,
  GridItem,
  Badge,
  Image,
  Spinner,
} from '@chakra-ui/react'
import { usePaymentMethodsQuery } from '@/services/usePaymentQuery'


interface PaymentMethodSelectorProps {
  currency: 'USD' | 'IDR'
  onMethodSelect: (method: string, type: 'invoice' | 'ewallet' | 'virtual_account' | 'retail_outlet' | 'credit_card') => void
  selectedMethod?: string
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  currency,
  onMethodSelect,
  selectedMethod
}) => {

  const { data: paymentMethods, isLoading, error } = usePaymentMethodsQuery(currency)

  const getMethodIcon = (method: string) => {
    const icons: { [key: string]: string } = {
      'OVO': '/icons/ovo.png',
      'DANA': '/icons/dana.png',
      'LINKAJA': '/icons/linkaja.png',
      'SHOPEEPAY': '/icons/shopeepay.png',
      'BCA': '/icons/bca.png',
      'BNI': '/icons/bni.png',
      'BRI': '/icons/bri.png',
      'MANDIRI': '/icons/mandiri.png',
      'PERMATA': '/icons/permata.png',
      'ALFAMART': '/icons/alfamart.png',
      'INDOMARET': '/icons/indomaret.png',
    }
    return icons[method] || '/icons/default-payment.png'
  }

  if (isLoading) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Payment Methods</Heading>
        </Card.Header>
        <Card.Body>
          <HStack>
            <Spinner size="sm" />
            <Text>Loading payment methods...</Text>
          </HStack>
        </Card.Body>
      </Card.Root>
    )
  }

  if (error || !paymentMethods) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Payment Methods</Heading>
        </Card.Header>
        <Card.Body>
          <Text color="red.500">Failed to load payment methods</Text>
        </Card.Body>
      </Card.Root>
    )
  }

  return (
    <Card.Root>
      <Card.Header>
        <Heading size="md">Choose Payment Method</Heading>
        <Text fontSize="sm" color="gray.600">
          Available for {currency}
        </Text>
      </Card.Header>
      <Card.Body>
        <VStack align="stretch" gap={6}>
          {/* Xendit Invoice (Recommended) */}
          {paymentMethods.invoice && (
            <Box>
              <HStack mb={3}>
                <Heading size="sm">Xendit Invoice</Heading>
                <Badge colorScheme="blue">Recommended</Badge>
              </HStack>
              <Button
                variant={selectedMethod === 'xendit_invoice' ? 'solid' : 'outline'}
                colorScheme="blue"
                w="full"
                h="auto"
                p={4}
                onClick={() => onMethodSelect('xendit_invoice', 'invoice')}
              >
                <VStack>
                  <Text fontWeight="bold">All-in-One Payment</Text>
                  <Text fontSize="sm" opacity={0.8}>
                    Credit Cards, Bank Transfer, E-Wallets & More
                  </Text>
                </VStack>
              </Button>
            </Box>
          )}

          {/* Credit Card */}
          {paymentMethods.creditCard && (
            <Box>
              <Heading size="sm" mb={3}>Credit Card</Heading>
              <Button
                variant={selectedMethod === 'credit_card' ? 'solid' : 'outline'}
                colorScheme="gray"
                w="full"
                h="auto"
                p={4}
                onClick={() => onMethodSelect('credit_card', 'credit_card')}
              >
                <VStack>
                  <Text fontWeight="bold">Credit Card</Text>
                  <Text fontSize="sm" opacity={0.8}>
                    Visa, Mastercard, JCB
                  </Text>
                </VStack>
              </Button>
            </Box>
          )}

          {/* E-Wallets */}
          {paymentMethods.ewallet.length > 0 && (
            <Box>
              <Heading size="sm" mb={3}>E-Wallets</Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                {paymentMethods.ewallet.map((wallet) => (
                  <GridItem key={wallet}>
                    <Button
                      variant={selectedMethod === wallet ? 'solid' : 'outline'}
                      colorScheme="green"
                      w="full"
                      h="auto"
                      p={3}
                      onClick={() => onMethodSelect(wallet, 'ewallet')}
                    >
                      <VStack gap={1}>
                        <Box boxSize="24px">
                          <Image
                            src={getMethodIcon(wallet)}
                            alt={wallet}
                            boxSize="24px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium">{wallet}</Text>
                      </VStack>
                    </Button>
                  </GridItem>
                ))}
              </Grid>
            </Box>
          )}

          {/* Virtual Account */}
          {paymentMethods.virtualAccount.length > 0 && (
            <Box>
              <Heading size="sm" mb={3}>Bank Transfer (Virtual Account)</Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                {paymentMethods.virtualAccount.map((bank) => (
                  <GridItem key={bank}>
                    <Button
                      variant={selectedMethod === bank ? 'solid' : 'outline'}
                      colorScheme="blue"
                      w="full"
                      h="auto"
                      p={3}
                      onClick={() => onMethodSelect(bank, 'virtual_account')}
                    >
                      <VStack gap={1}>
                        <Box boxSize="24px">
                          <Image
                            src={getMethodIcon(bank)}
                            alt={bank}
                            boxSize="24px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium">{bank}</Text>
                      </VStack>
                    </Button>
                  </GridItem>
                ))}
              </Grid>
            </Box>
          )}

          {/* Retail Outlets */}
          {paymentMethods.retailOutlet.length > 0 && (
            <Box>
              <Heading size="sm" mb={3}>Retail Outlets</Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                {paymentMethods.retailOutlet.map((outlet) => (
                  <GridItem key={outlet}>
                    <Button
                      variant={selectedMethod === outlet ? 'solid' : 'outline'}
                      colorScheme="orange"
                      w="full"
                      h="auto"
                      p={3}
                      onClick={() => onMethodSelect(outlet, 'retail_outlet')}
                    >
                      <VStack gap={1}>
                        <Box boxSize="24px">
                          <Image
                            src={getMethodIcon(outlet)}
                            alt={outlet}
                            boxSize="24px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium">{outlet}</Text>
                      </VStack>
                    </Button>
                  </GridItem>
                ))}
              </Grid>
            </Box>
          )}

          {/* Payment Security Info */}
          <Box bg="gray.50" p={4} borderRadius="md">
            <Text fontSize="sm" color="gray.700" textAlign="center">
              🔒 All payments are secured by Xendit with 256-bit SSL encryption
            </Text>
          </Box>
        </VStack>
      </Card.Body>
    </Card.Root>
  )
}

export default PaymentMethodSelector
