'use client'
import React from 'react'
import {
  Box,
  Card,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Image,
  Spinner,
  RadioCard,
} from '@chakra-ui/react'
import { usePaymentMethodsQuery } from '@/services/usePaymentQuery'
import { FaShieldAlt, FaLock, FaCreditCard, FaWallet, FaUniversity, FaStore } from 'react-icons/fa'
import { useTranslations } from 'next-intl'


interface PaymentMethodSelectorProps {
  currency: 'USD' | 'IDR'
  onMethodSelect: (method: string, type: 'invoice' | 'ewallet' | 'virtual_account' | 'retail_outlet' | 'credit_card') => void
  selectedMethod?: string
  value?: string
  onChange?: (value: string) => void
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  currency,
  onMethodSelect,
  selectedMethod,
  value,
  onChange
}) => {
  const t = useTranslations()
  const { data: paymentMethods, isLoading, error } = usePaymentMethodsQuery(currency)

  // Handle radio card change
  const handleRadioChange = ({ value: selectedValue }: { value: string | null }) => {
    if (!selectedValue) return

    if (onChange) {
      onChange(selectedValue)
    }

    // Parse the selected value to extract method and type
    const [method, type] = selectedValue.split('|')
    onMethodSelect(method, type as any)
  }

  const getMethodIcon = (method: string) => {
    const icons: { [key: string]: string } = {
      'OVO': '/icons/ovo.png',
      'DANA': '/icons/dana.png',
      'LINKAJA': '/icons/linkaja.png',
      'SHOPEEPAY': '/icons/shopeepay.png',
      'BCA': '/icons/bca.png',
      'BNI': '/icons/bni.png',
      'BRI': '/icons/bri.png',
      'MANDIRI': '/icons/mandiri.png',
      'PERMATA': '/icons/permata.png',
      'ALFAMART': '/icons/alfamart.png',
      'INDOMARET': '/icons/indomaret.png',
    }
    return icons[method] || '/icons/default-payment.png'
  }

  if (isLoading) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">{t('Payment.method')}</Heading>
        </Card.Header>
        <Card.Body>
          <HStack>
            <Spinner size="sm" />
            <Text>Loading payment methods...</Text>
          </HStack>
        </Card.Body>
      </Card.Root>
    )
  }

  if (error || !paymentMethods) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">{t('Payment.method')}</Heading>
        </Card.Header>
        <Card.Body>
          <Text color="red.500">Failed to load payment methods</Text>
        </Card.Body>
      </Card.Root>
    )
  }

  return (
    <Card.Root>
      <Card.Header>
        <HStack justify="space-between" align="center">
          <VStack align="start" gap={1}>
            <Heading size="md">{t('Payment.chooseMethod')}</Heading>
            <Text fontSize="sm" color="gray.600">
              {t('Payment.availableFor')} {currency}
            </Text>
          </VStack>
          <HStack gap={2}>
            <FaShieldAlt color="green" />
            <Text fontSize="xs" color="green.600" fontWeight="medium">
              {t('Payment.securedBy')}
            </Text>
          </HStack>
        </HStack>
      </Card.Header>
      <Card.Body>
        <RadioCard.Root
          value={value || selectedMethod || ''}
          onValueChange={handleRadioChange}
        >
          <VStack align="stretch" gap={4}>
            {/* Xendit Invoice (Recommended) */}
            {paymentMethods.invoice && (
              <Box>
                <HStack mb={3}>
                  <FaCreditCard />
                  <Heading size="sm">Xendit Invoice</Heading>
                  <Badge colorScheme="blue">Recommended</Badge>
                </HStack>
                <RadioCard.Item value="xendit_invoice|invoice">
                  <RadioCard.ItemHiddenInput />
                  <RadioCard.ItemControl>
                    <HStack gap={3} align="center" w="full">
                      <RadioCard.ItemIndicator />
                      <VStack align="start" flex="1" gap={1}>
                        <Text fontWeight="bold">{t('Payment.allInOne')}</Text>
                        <Text fontSize="sm" color="gray.600">
                          {t('Payment.allInOneDesc')}
                        </Text>
                      </VStack>
                    </HStack>
                  </RadioCard.ItemControl>
                </RadioCard.Item>
              </Box>
            )}

          {/* Credit Card */}
          {paymentMethods.creditCard && (
            <Box>
              <HStack mb={3}>
                <FaCreditCard />
                <Heading size="sm">{t('Payment.creditCard')}</Heading>
              </HStack>
              <RadioCard.Item value="credit_card|credit_card">
                <RadioCard.ItemHiddenInput />
                <RadioCard.ItemControl>
                  <HStack gap={3} align="center" w="full">
                    <RadioCard.ItemIndicator />
                    <VStack align="start" flex="1" gap={1}>
                      <Text fontWeight="bold">{t('Payment.creditCard')}</Text>
                      <Text fontSize="sm" color="gray.600">
                        {t('Payment.creditCardDesc')}
                      </Text>
                    </VStack>
                  </HStack>
                </RadioCard.ItemControl>
              </RadioCard.Item>
            </Box>
          )}

          {/* E-Wallets */}
          {paymentMethods.ewallet.length > 0 && (
            <Box>
              <HStack mb={3}>
                <FaWallet />
                <Heading size="sm">{t('Payment.eWallets')}</Heading>
              </HStack>
              <VStack gap={2}>
                {paymentMethods.ewallet.map((wallet) => (
                  <RadioCard.Item key={wallet} value={`${wallet}|ewallet`} w="full">
                    <RadioCard.ItemHiddenInput />
                    <RadioCard.ItemControl>
                      <HStack gap={3} align="center" w="full">
                        <RadioCard.ItemIndicator />
                        <Box boxSize="24px">
                          <Image
                            src={getMethodIcon(wallet)}
                            alt={wallet}
                            boxSize="24px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium" flex="1">{wallet}</Text>
                      </HStack>
                    </RadioCard.ItemControl>
                  </RadioCard.Item>
                ))}
              </VStack>
            </Box>
          )}

          {/* Virtual Account */}
          {paymentMethods.virtualAccount.length > 0 && (
            <Box>
              <HStack mb={3}>
                <FaUniversity />
                <Heading size="sm">{t('Payment.bankTransfer')}</Heading>
              </HStack>
              <VStack gap={2}>
                {paymentMethods.virtualAccount.map((bank) => (
                  <RadioCard.Item key={bank} value={`${bank}|virtual_account`} w="full">
                    <RadioCard.ItemHiddenInput />
                    <RadioCard.ItemControl>
                      <HStack gap={3} align="center" w="full">
                        <RadioCard.ItemIndicator />
                        <Box boxSize="24px">
                          <Image
                            src={getMethodIcon(bank)}
                            alt={bank}
                            boxSize="24px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium" flex="1">{bank}</Text>
                      </HStack>
                    </RadioCard.ItemControl>
                  </RadioCard.Item>
                ))}
              </VStack>
            </Box>
          )}

          {/* Retail Outlets */}
          {paymentMethods.retailOutlet.length > 0 && (
            <Box>
              <HStack mb={3}>
                <FaStore />
                <Heading size="sm">{t('Payment.retailOutlets')}</Heading>
              </HStack>
              <VStack gap={2}>
                {paymentMethods.retailOutlet.map((outlet) => (
                  <RadioCard.Item key={outlet} value={`${outlet}|retail_outlet`} w="full">
                    <RadioCard.ItemHiddenInput />
                    <RadioCard.ItemControl>
                      <HStack gap={3} align="center" w="full">
                        <RadioCard.ItemIndicator />
                        <Box boxSize="24px">
                          <Image
                            src={getMethodIcon(outlet)}
                            alt={outlet}
                            boxSize="24px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium" flex="1">{outlet}</Text>
                      </HStack>
                    </RadioCard.ItemControl>
                  </RadioCard.Item>
                ))}
              </VStack>
            </Box>
          )}

          </VStack>
        </RadioCard.Root>

        {/* Payment Security Info */}
        <Box bg="green.50" p={4} borderRadius="md" mt={4} border="1px solid" borderColor="green.200">
          <HStack justify="center" gap={2}>
            <FaLock color="green" />
            <Text fontSize="sm" color="green.700" textAlign="center" fontWeight="medium">
              {t('Payment.securityInfo')}
            </Text>
          </HStack>
        </Box>
      </Card.Body>
    </Card.Root>
  )
}

export default PaymentMethodSelector
