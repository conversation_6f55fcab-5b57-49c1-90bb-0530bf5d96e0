{"language": "Bahasa", "currency": "<PERSON>", "save": "Simpan", "cancel": "Batalkan", "delete": "Hapus", "edit": "Sunting", "update": "<PERSON><PERSON><PERSON>", "add": "Tambah", "view": "Lihat", "viewAll": "<PERSON><PERSON>a", "showing": "Menampilkan", "of": "dari", "bids": "<PERSON><PERSON><PERSON>", "Navbar": {"languageCurrencyChangeText": "<PERSON>ur <PERSON> dan <PERSON>", "placeholderSearch": "<PERSON><PERSON> kole<PERSON>i..."}, "Button": {"login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>", "sellNow": "<PERSON><PERSON>", "sell": "<PERSON><PERSON>", "forgotPassword": "Lupa Kata Sandi?", "resetPassword": "Atur ulang kata sandi", "sendResetLink": "<PERSON><PERSON> atur ulang", "updateProfile": "<PERSON><PERSON><PERSON> profil", "changePassword": "Ubah kata sandi", "deleteAccount": "<PERSON><PERSON> akun"}, "Payment": {"status": "Status Pembayaran", "pending": "<PERSON><PERSON><PERSON>", "paid": "Pembayaran Berhasil", "failed": "Pembayaran Gagal", "expired": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "Pembayaran Dibatalkan", "amount": "<PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON>", "channel": "<PERSON><PERSON><PERSON>", "paidAt": "Dibayar <PERSON>", "createdAt": "Dibuat Pada", "retryPayment": "Coba Lagi <PERSON>", "createNewPayment": "Buat Pembayaran Baru", "processingMessage": "Pembayaran Anda sedang diproses. Ini mungkin memakan waktu beberapa menit.", "failedMessage": "Pembayaran gagal. <PERSON>lakan coba lagi atau gunakan metode pembayaran yang berbeda.", "expiredMessage": "Tautan pembayaran telah kedalu<PERSON>sa. <PERSON><PERSON><PERSON> buat pembayaran baru.", "successMessage": "Pembayaran berhasil diselesaikan! Pesanan Anda sedang diproses.", "securityInfo": "<PERSON><PERSON><PERSON> pem<PERSON>aran diamankan oleh Xendit dengan enkripsi SSL 256-bit", "chooseMethod": "<PERSON><PERSON><PERSON>", "availableFor": "Tersedia untuk", "securedBy": "Diamankan o<PERSON>", "allInOne": "Pembayaran All-in-One", "allInOneDesc": "Kartu Kredit, Transfer Bank, E-Wallet & Lainnya", "creditCard": "Kartu Kredit", "creditCardDesc": "Visa, Mastercard, JCB", "eWallets": "E-Wallet", "bankTransfer": "Transfer Bank (Virtual Account)", "retailOutlets": "Outlet Ritel"}, "Currency": {"usd": "<PERSON><PERSON>", "idr": "<PERSON><PERSON><PERSON>", "selectCurrency": "<PERSON><PERSON><PERSON>", "conversionRate": "<PERSON><PERSON>"}, "Product": {"currentBid": "<PERSON><PERSON><PERSON>", "startingBid": "<PERSON><PERSON><PERSON>", "bidCount": "<PERSON><PERSON><PERSON>", "timeLeft": "<PERSON><PERSON><PERSON>", "endsIn": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "placeBid": "<PERSON><PERSON><PERSON>", "bidNow": "<PERSON><PERSON>", "autoBid": "<PERSON><PERSON>", "buyNow": "<PERSON><PERSON>", "addToCart": "Tambah ke Keranjang", "addToWishlist": "Tambah ke Wishlist", "removeFromWishlist": "<PERSON><PERSON>", "showBidHistory": "<PERSON><PERSON> riwayat tawaran", "viewSalesHistory": "Lihat riwayat penjualan", "auctionStatus": "Status Lelang", "active": "Aktif", "ended": "<PERSON><PERSON><PERSON>", "upcoming": "<PERSON><PERSON>", "auctionNotStarted": "<PERSON><PERSON> belum dimulai", "auctionEnded": "Lelang telah be<PERSON>hir", "highestBidder": "<PERSON>a adalah penawar tertinggi", "outbid": "<PERSON>a telah di<PERSON>", "loading": "Memuat...", "notFound": "Produk tidak ditemukan", "description": "<PERSON><PERSON><PERSON><PERSON>", "specifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "seller": "<PERSON><PERSON><PERSON>", "shippingInfo": "Informasi Pengiriman", "returnPolicy": "<PERSON><PERSON><PERSON><PERSON>"}, "Auction": {"title": "Lelang", "live": "Lelang Langsung", "upcoming": "Lelang Mendatang", "ended": "<PERSON><PERSON>", "myBids": "<PERSON><PERSON><PERSON>", "watchlist": "<PERSON><PERSON><PERSON>", "bidAmount": "<PERSON><PERSON><PERSON>", "maxBid": "<PERSON><PERSON><PERSON>", "bidIncrement": "<PERSON><PERSON><PERSON>", "autoBidEnabled": "<PERSON><PERSON> otom<PERSON>", "autoBidDisabled": "<PERSON><PERSON> otomati<PERSON>", "enableAutoBid": "<PERSON><PERSON><PERSON><PERSON>", "disableAutoBid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bidHistory": "<PERSON><PERSON><PERSON><PERSON>", "noBidsYet": "Belum ada tawaran", "firstBid": "<PERSON><PERSON><PERSON> yang pertama menawar!", "minimumBid": "<PERSON><PERSON><PERSON> minimum", "bidTooLow": "<PERSON><PERSON><PERSON> tawaran terlalu rendah", "bidSuccess": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON> di<PERSON>at", "bidError": "<PERSON><PERSON> memb<PERSON>t tawaran", "extendedBidding": "Perpanjangan Lelang", "extendedBiddingInfo": "Lelang akan diperpanjang jika ada tawaran di menit-menit terakhir"}, "Cart": {"title": "Keranjang Belanja", "empty": "Keranjang Anda kosong", "items": "barang", "item": "barang", "subtotal": "Subtotal", "shipping": "Pen<PERSON><PERSON>", "tax": "<PERSON><PERSON>", "total": "Total", "checkout": "Checkout", "continueShopping": "<PERSON><PERSON><PERSON><PERSON>", "removeItem": "<PERSON><PERSON> bar<PERSON>", "updateQuantity": "<PERSON><PERSON><PERSON> jum<PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "itemTotal": "Total Barang"}, "Checkout": {"title": "Checkout", "orderSummary": "<PERSON><PERSON><PERSON>", "shippingAddress": "<PERSON><PERSON><PERSON>", "billingAddress": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "placeOrder": "<PERSON><PERSON><PERSON>", "processing": "Memproses...", "orderConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderNumber": "<PERSON><PERSON>", "estimatedDelivery": "<PERSON><PERSON><PERSON><PERSON>", "trackOrder": "<PERSON><PERSON>"}}