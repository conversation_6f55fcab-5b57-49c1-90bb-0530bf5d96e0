'use client'
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useLocale } from 'next-intl'

// Types
export type SupportedCurrency = 'USD' | 'IDR'
export type SupportedLanguage = 'en' | 'id'

interface CurrencyLanguageContextType {
  // Currency state
  currency: SupportedCurrency
  setCurrency: (currency: SupportedCurrency) => void
  
  // Language state
  language: SupportedLanguage
  setLanguage: (language: SupportedLanguage) => void
  
  // Helper functions
  formatPrice: (amount: number) => string
  getCurrencySymbol: () => string
  getLanguageLabel: () => string
  getCurrencyLabel: () => string
}

const CurrencyLanguageContext = createContext<CurrencyLanguageContextType | undefined>(undefined)

interface CurrencyLanguageProviderProps {
  children: ReactNode
}

export const CurrencyLanguageProvider: React.FC<CurrencyLanguageProviderProps> = ({ children }) => {
  const router = useRouter()
  const pathname = usePathname()
  const currentLocale = useLocale() as SupportedLanguage
  
  // Initialize states
  const [currency, setCurrencyState] = useState<SupportedCurrency>('USD')
  const [language, setLanguageState] = useState<SupportedLanguage>(currentLocale)

  // Load saved preferences from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCurrency = localStorage.getItem('preferred-currency') as SupportedCurrency
      const savedLanguage = localStorage.getItem('preferred-language') as SupportedLanguage
      
      if (savedCurrency && ['USD', 'IDR'].includes(savedCurrency)) {
        setCurrencyState(savedCurrency)
      } else {
        // Set default currency based on language
        setCurrencyState(currentLocale === 'id' ? 'IDR' : 'USD')
      }
      
      if (savedLanguage && ['en', 'id'].includes(savedLanguage)) {
        setLanguageState(savedLanguage)
      } else {
        setLanguageState(currentLocale)
      }
    }
  }, [currentLocale])

  // Update language when locale changes
  useEffect(() => {
    setLanguageState(currentLocale)
  }, [currentLocale])

  // Currency setter with localStorage persistence
  const setCurrency = (newCurrency: SupportedCurrency) => {
    setCurrencyState(newCurrency)
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-currency', newCurrency)
    }
  }

  // Language setter with navigation and localStorage persistence
  const setLanguage = (newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage)
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-language', newLanguage)
    }
    
    // Navigate to new locale
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    router.push(`/${newLanguage}${currentPath}`)
  }

  // Helper functions
  const formatPrice = (amount: number): string => {
    const locale = currency === 'USD' ? 'en-US' : 'id-ID'
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'IDR' ? 0 : 2,
      maximumFractionDigits: currency === 'IDR' ? 0 : 2,
    }).format(amount)
  }

  const getCurrencySymbol = (): string => {
    return currency === 'USD' ? '$' : 'Rp'
  }

  const getLanguageLabel = (): string => {
    return language === 'en' ? 'English' : 'Bahasa Indonesia'
  }

  const getCurrencyLabel = (): string => {
    return currency === 'USD' ? 'USD ($)' : 'IDR (Rp)'
  }

  const value: CurrencyLanguageContextType = {
    currency,
    setCurrency,
    language,
    setLanguage,
    formatPrice,
    getCurrencySymbol,
    getLanguageLabel,
    getCurrencyLabel,
  }

  return (
    <CurrencyLanguageContext.Provider value={value}>
      {children}
    </CurrencyLanguageContext.Provider>
  )
}

// Custom hook to use the context
export const useCurrencyLanguage = (): CurrencyLanguageContextType => {
  const context = useContext(CurrencyLanguageContext)
  if (context === undefined) {
    throw new Error('useCurrencyLanguage must be used within a CurrencyLanguageProvider')
  }
  return context
}

// Currency options for dropdowns
export const CURRENCY_OPTIONS = [
  {
    value: 'USD' as const,
    label: 'USD ($)',
    symbol: '$',
    name: 'US Dollar',
    flag: '🇺🇸',
  },
  {
    value: 'IDR' as const,
    label: 'IDR (Rp)',
    symbol: 'Rp',
    name: 'Indonesian Rupiah',
    flag: '🇮🇩',
  },
]

// Language options for dropdowns
export const LANGUAGE_OPTIONS = [
  {
    value: 'en' as const,
    label: 'English',
    name: 'English',
    flag: '🇺🇸',
  },
  {
    value: 'id' as const,
    label: 'Bahasa Indonesia',
    name: 'Indonesian',
    flag: '🇮🇩',
  },
]

// Helper function to get currency by language
export const getCurrencyByLanguage = (language: SupportedLanguage): SupportedCurrency => {
  return language === 'id' ? 'IDR' : 'USD'
}

// Helper function to get language by currency
export const getLanguageByCurrency = (currency: SupportedCurrency): SupportedLanguage => {
  return currency === 'IDR' ? 'id' : 'en'
}
