import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

// Types
export interface CreateInvoiceData {
  orderId: string;
  currency?: 'IDR' | 'USD';
  customerEmail: string;
  customerName: string;
  description?: string;
  successRedirectUrl?: string;
  failureRedirectUrl?: string;
}

export interface CreateEWalletData {
  orderId: string;
  currency?: 'IDR' | 'USD';
  ewalletType: 'OVO' | 'DANA' | 'LINKAJA' | 'SHOPEEPAY';
  customerPhone: string;
  customerName: string;
}

export interface InvoiceResponse {
  id: string;
  externalId: string;
  status: string;
  amount: number;
  currency: string;
  invoiceUrl: string;
  expiryDate: string;
  paymentMethod?: string;
}

export interface PaymentStatus {
  id: string;
  orderId: string;
  status: 'PENDING' | 'PAID' | 'FAILED' | 'EXPIRED' | 'CANCELLED';
  amount: number;
  currency: string;
  paidAt: string | null;
  paymentMethod: string | null;
  paymentChannel: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethods {
  invoice: boolean;
  ewallet: string[];
  virtualAccount: string[];
  retailOutlet: string[];
  creditCard: boolean;
}

export interface PaymentHistoryItem {
  id: string;
  orderId: string;
  orderNumber: string;
  status: string;
  amount: number;
  currency: string;
  paymentMethod: string | null;
  paidAt: string | null;
  createdAt: string;
}

export interface PaymentHistory {
  payments: PaymentHistoryItem[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Query keys
export const paymentQueryKeys = {
  all: ['payments'] as const,
  status: (orderId: string) => [...paymentQueryKeys.all, 'status', orderId] as const,
  methods: (currency: string) => [...paymentQueryKeys.all, 'methods', currency] as const,
  history: (params?: any) => [...paymentQueryKeys.all, 'history', params] as const,
};

// Create Invoice Mutation
export const useCreateInvoiceMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateInvoiceData): Promise<InvoiceResponse> => {
      const response = await apiClient.post('/payments/invoice', data);
      return response.data;
    },
    onSuccess: (data) => {
      toaster.create({
        title: "Invoice Created",
        description: "Payment invoice has been created successfully.",
        type: "success",
      });
      
      // Invalidate payment status queries
      queryClient.invalidateQueries({ 
        queryKey: paymentQueryKeys.status(data.externalId) 
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to create payment invoice",
        type: "error",
      });
    },
  });
};

// Create eWallet Charge Mutation
export const useCreateEWalletMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateEWalletData): Promise<any> => {
      const response = await apiClient.post('/payments/ewallet', data);
      return response.data;
    },
    onSuccess: () => {
      toaster.create({
        title: "eWallet Charge Created",
        description: "eWallet payment has been initiated successfully.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Payment Error",
        description: error instanceof Error ? error.message : "Failed to create eWallet charge",
        type: "error",
      });
    },
  });
};

// Get Payment Status Query
export const usePaymentStatusQuery = (orderId: string, enabled: boolean = true) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: paymentQueryKeys.status(orderId),
    queryFn: async (): Promise<PaymentStatus> => {
      const response = await apiClient.get(`/payments/order/${orderId}/status`);
      return response.data;
    },
    enabled: !!orderId && enabled,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: (data) => {
      // Auto-refetch every 5 seconds if payment is still pending
      return data?.status === 'PENDING' ? 5000 : false;
    },
  });
};

// Get Payment Methods Query
export const usePaymentMethodsQuery = (currency: 'IDR' | 'USD') => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: paymentQueryKeys.methods(currency),
    queryFn: async (): Promise<PaymentMethods> => {
      const response = await apiClient.get('/payments/methods', {
        params: { currency }
      });
      return response.data;
    },
    enabled: !!currency,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Payment History Query
export const usePaymentHistoryQuery = (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: paymentQueryKeys.history(params),
    queryFn: async (): Promise<PaymentHistory> => {
      const response = await apiClient.get('/payments/history', {
        params
      });
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get Invoice Status Query
export const useInvoiceStatusQuery = (invoiceId: string, enabled: boolean = true) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: [...paymentQueryKeys.all, 'invoice', invoiceId],
    queryFn: async (): Promise<any> => {
      const response = await apiClient.get(`/payments/invoice/${invoiceId}/status`);
      return response.data;
    },
    enabled: !!invoiceId && enabled,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: (data) => {
      // Auto-refetch every 5 seconds if payment is still pending
      return data?.status === 'PENDING' ? 5000 : false;
    },
  });
};

// Utility function to open payment URL
export const openPaymentUrl = (url: string) => {
  // Open in new tab/window
  const paymentWindow = window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
  
  if (!paymentWindow) {
    // Fallback if popup is blocked
    window.location.href = url;
  }
  
  return paymentWindow;
};

// Utility function to format currency
export const formatCurrency = (amount: number, currency: 'USD' | 'IDR') => {
  return new Intl.NumberFormat(currency === 'USD' ? 'en-US' : 'id-ID', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currency === 'IDR' ? 0 : 2,
  }).format(amount);
};

// Payment details query
export const usePaymentDetailsQuery = (paymentId: string) => {
  const apiClient = useAuthenticatedApi()

  return useQuery({
    queryKey: ['payment-details', paymentId],
    queryFn: async () => {
      const response = await apiClient.get(`/payments/${paymentId}`)
      return response.data
    },
    enabled: !!paymentId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: (data) => {
      // Auto-refetch if payment is still pending
      return data?.payment?.status === 'PENDING' ? 10000 : false // 10 seconds
    }
  })
}
