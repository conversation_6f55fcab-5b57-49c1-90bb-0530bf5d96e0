import { SupportedCurrency } from '@/contexts/CurrencyLanguageContext'

// Exchange rates - in production, this should come from a real-time API
const EXCHANGE_RATES = {
  USD_TO_IDR: 15000,
  IDR_TO_USD: 1 / 15000,
} as const

export interface CurrencyConversionOptions {
  fromCurrency: SupportedCurrency
  toCurrency: SupportedCurrency
  amount: number
}

/**
 * Convert amount from one currency to another
 */
export function convertCurrency({ fromCurrency, toCurrency, amount }: CurrencyConversionOptions): number {
  if (fromCurrency === toCurrency) {
    return amount
  }

  if (fromCurrency === 'USD' && toCurrency === 'IDR') {
    return amount * EXCHANGE_RATES.USD_TO_IDR
  }

  if (fromCurrency === 'IDR' && toCurrency === 'USD') {
    return amount * EXCHANGE_RATES.IDR_TO_USD
  }

  return amount
}

/**
 * Format price with proper currency symbol and locale
 */
export function formatPrice(amount: number, currency: SupportedCurrency): string {
  const locale = currency === 'USD' ? 'en-US' : 'id-ID'
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currency === 'IDR' ? 0 : 2,
    maximumFractionDigits: currency === 'IDR' ? 0 : 2,
  }).format(amount)
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: SupportedCurrency): string {
  return currency === 'USD' ? '$' : 'Rp'
}

/**
 * Convert and format price for display
 */
export function convertAndFormatPrice(
  amount: number, 
  fromCurrency: SupportedCurrency, 
  toCurrency: SupportedCurrency
): string {
  const convertedAmount = convertCurrency({ fromCurrency, toCurrency, amount })
  return formatPrice(convertedAmount, toCurrency)
}

/**
 * Parse price string and extract numeric value
 */
export function parsePrice(priceString: string): number {
  // Remove all non-numeric characters except decimal point and minus sign
  const numericString = priceString.replace(/[^0-9.-]+/g, '')
  return parseFloat(numericString) || 0
}

/**
 * Get exchange rate between two currencies
 */
export function getExchangeRate(fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency): number {
  if (fromCurrency === toCurrency) {
    return 1
  }

  if (fromCurrency === 'USD' && toCurrency === 'IDR') {
    return EXCHANGE_RATES.USD_TO_IDR
  }

  if (fromCurrency === 'IDR' && toCurrency === 'USD') {
    return EXCHANGE_RATES.IDR_TO_USD
  }

  return 1
}

/**
 * Update exchange rates (for future real-time API integration)
 */
export async function updateExchangeRates(): Promise<void> {
  // TODO: Implement real-time exchange rate fetching
  // This would typically call an external API like:
  // - https://api.exchangerate-api.com/
  // - https://openexchangerates.org/
  // - https://fixer.io/
  console.log('Exchange rates updated (placeholder)')
}
