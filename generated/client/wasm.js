
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  emailVerified: 'emailVerified',
  phoneNumber: 'phoneNumber',
  phoneVerified: 'phoneVerified',
  role: 'role',
  oauthProvider: 'oauthProvider',
  oauthId: 'oauthId',
  password: 'password',
  image: 'image',
  isActive: 'isActive',
  isEmailVerified: 'isEmailVerified',
  isPhoneVerified: 'isPhoneVerified',
  isBlocked: 'isBlocked',
  country: 'country',
  ipAddress: 'ipAddress',
  lastLogin: 'lastLogin',
  lastLoginIp: 'lastLoginIp',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  googleId: 'googleId',
  refreshToken: 'refreshToken'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  sellType: 'sellType'
};

exports.Prisma.ItemTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  categoryId: 'categoryId',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  description: 'description',
  sellType: 'sellType',
  priceUSD: 'priceUSD',
  auctionStartDate: 'auctionStartDate',
  auctionEndDate: 'auctionEndDate',
  currentBid: 'currentBid',
  bidCount: 'bidCount',
  extendedBiddingEnabled: 'extendedBiddingEnabled',
  extendedBiddingMinutes: 'extendedBiddingMinutes',
  extendedBiddingDuration: 'extendedBiddingDuration',
  status: 'status',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  sellerId: 'sellerId',
  categoryId: 'categoryId',
  itemTypeId: 'itemTypeId',
  slug: 'slug'
};

exports.Prisma.ProductImageScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  imageUrl: 'imageUrl',
  altText: 'altText',
  sortOrder: 'sortOrder',
  isMain: 'isMain',
  createdAt: 'createdAt'
};

exports.Prisma.BidScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  bidderId: 'bidderId',
  amount: 'amount',
  isWinning: 'isWinning',
  createdAt: 'createdAt'
};

exports.Prisma.CartScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CartItemScalarFieldEnum = {
  id: 'id',
  cartId: 'cartId',
  productId: 'productId',
  quantity: 'quantity',
  price: 'price',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ShippingAddressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  address: 'address',
  city: 'city',
  provinceRegion: 'provinceRegion',
  zipCode: 'zipCode',
  country: 'country',
  isDefault: 'isDefault',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderNumber: 'orderNumber',
  status: 'status',
  paymentStatus: 'paymentStatus',
  paymentMethod: 'paymentMethod',
  subtotal: 'subtotal',
  shippingCost: 'shippingCost',
  tax: 'tax',
  total: 'total',
  currency: 'currency',
  shippingAddressId: 'shippingAddressId',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId',
  quantity: 'quantity',
  price: 'price',
  createdAt: 'createdAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  xenditInvoiceId: 'xenditInvoiceId',
  xenditPaymentId: 'xenditPaymentId',
  externalId: 'externalId',
  status: 'status',
  amount: 'amount',
  currency: 'currency',
  paymentMethod: 'paymentMethod',
  paymentChannel: 'paymentChannel',
  invoiceUrl: 'invoiceUrl',
  paidAt: 'paidAt',
  expiredAt: 'expiredAt',
  failureReason: 'failureReason',
  webhookData: 'webhookData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phoneNumber: 'phoneNumber',
  role: 'role',
  oauthProvider: 'oauthProvider',
  oauthId: 'oauthId',
  password: 'password',
  image: 'image',
  country: 'country',
  ipAddress: 'ipAddress',
  lastLoginIp: 'lastLoginIp',
  googleId: 'googleId',
  refreshToken: 'refreshToken'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  sellType: 'sellType'
};

exports.Prisma.ItemTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  categoryId: 'categoryId'
};

exports.Prisma.ProductOrderByRelevanceFieldEnum = {
  id: 'id',
  itemName: 'itemName',
  description: 'description',
  sellType: 'sellType',
  status: 'status',
  sellerId: 'sellerId',
  categoryId: 'categoryId',
  itemTypeId: 'itemTypeId',
  slug: 'slug'
};

exports.Prisma.ProductImageOrderByRelevanceFieldEnum = {
  id: 'id',
  productId: 'productId',
  imageUrl: 'imageUrl',
  altText: 'altText'
};

exports.Prisma.BidOrderByRelevanceFieldEnum = {
  id: 'id',
  productId: 'productId',
  bidderId: 'bidderId'
};

exports.Prisma.CartOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.CartItemOrderByRelevanceFieldEnum = {
  id: 'id',
  cartId: 'cartId',
  productId: 'productId'
};

exports.Prisma.ShippingAddressOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  address: 'address',
  city: 'city',
  provinceRegion: 'provinceRegion',
  zipCode: 'zipCode',
  country: 'country'
};

exports.Prisma.OrderOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderNumber: 'orderNumber',
  status: 'status',
  paymentStatus: 'paymentStatus',
  paymentMethod: 'paymentMethod',
  currency: 'currency',
  shippingAddressId: 'shippingAddressId',
  notes: 'notes'
};

exports.Prisma.OrderItemOrderByRelevanceFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.PaymentOrderByRelevanceFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  xenditInvoiceId: 'xenditInvoiceId',
  xenditPaymentId: 'xenditPaymentId',
  externalId: 'externalId',
  status: 'status',
  currency: 'currency',
  paymentMethod: 'paymentMethod',
  paymentChannel: 'paymentChannel',
  invoiceUrl: 'invoiceUrl',
  failureReason: 'failureReason'
};


exports.Prisma.ModelName = {
  User: 'User',
  Category: 'Category',
  ItemType: 'ItemType',
  Product: 'Product',
  ProductImage: 'ProductImage',
  Bid: 'Bid',
  Cart: 'Cart',
  CartItem: 'CartItem',
  ShippingAddress: 'ShippingAddress',
  Order: 'Order',
  OrderItem: 'OrderItem',
  Payment: 'Payment'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
