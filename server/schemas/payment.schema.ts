import { z } from "zod";

// Payment status enum
export const paymentStatusEnum = z.enum([
  "PENDING",
  "PAID", 
  "FAILED",
  "EXPIRED",
  "CANCELLED"
]);

// Currency enum
export const currencyEnum = z.enum(["USD", "IDR"]);

// Payment method enum
export const paymentMethodEnum = z.enum([
  "credit_card",
  "debit_card", 
  "bank_transfer",
  "ewallet",
  "virtual_account",
  "retail_outlet"
]);

// Create invoice schema
export const createInvoiceSchema = z.object({
  orderId: z.string().uuid("Invalid order ID"),
  currency: currencyEnum.optional().default("USD"),
  customerEmail: z.string().email("Invalid email"),
  customerName: z.string().min(1, "Customer name is required"),
  description: z.string().optional(),
  successRedirectUrl: z.string().url().optional(),
  failureRedirectUrl: z.string().url().optional(),
});

export type CreateInvoiceInput = z.infer<typeof createInvoiceSchema>;

// Create eWallet charge schema
export const createEWalletChargeSchema = z.object({
  orderId: z.string().uuid("Invalid order ID"),
  currency: currencyEnum.optional().default("USD"),
  ewalletType: z.enum(["OVO", "DANA", "LINKAJA", "SHOPEEPAY"]),
  customerPhone: z.string().min(10, "Valid phone number is required"),
  customerName: z.string().min(1, "Customer name is required"),
});

export type CreateEWalletChargeInput = z.infer<typeof createEWalletChargeSchema>;

// Webhook payload schema
export const webhookPayloadSchema = z.object({
  id: z.string(),
  external_id: z.string(),
  user_id: z.string().optional(),
  status: z.string(),
  merchant_name: z.string().optional(),
  amount: z.number(),
  paid_amount: z.number().optional(),
  bank_code: z.string().optional(),
  paid_at: z.string().optional(),
  payer_email: z.string().optional(),
  description: z.string().optional(),
  payment_method: z.string().optional(),
  payment_channel: z.string().optional(),
  payment_destination: z.string().optional(),
  currency: z.string().optional(),
  created: z.string().optional(),
  updated: z.string().optional(),
  // Additional fields for different payment types
  ewallet_type: z.string().optional(),
  failure_redirect_url: z.string().optional(),
  success_redirect_url: z.string().optional(),
});

export type WebhookPayload = z.infer<typeof webhookPayloadSchema>;

// Invoice response schema
export const invoiceResponseSchema = z.object({
  id: z.string(),
  externalId: z.string(),
  status: z.string(),
  amount: z.number(),
  currency: z.string(),
  invoiceUrl: z.string(),
  expiryDate: z.string(),
  paymentMethod: z.string().optional(),
});

export type InvoiceResponse = z.infer<typeof invoiceResponseSchema>;

// Payment status response schema
export const paymentStatusResponseSchema = z.object({
  id: z.string(),
  orderId: z.string(),
  status: paymentStatusEnum,
  amount: z.number(),
  currency: currencyEnum,
  paidAt: z.string().nullable(),
  paymentMethod: z.string().nullable(),
  paymentChannel: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type PaymentStatusResponse = z.infer<typeof paymentStatusResponseSchema>;

// Payment methods response schema
export const paymentMethodsResponseSchema = z.object({
  invoice: z.boolean(),
  ewallet: z.array(z.string()),
  virtualAccount: z.array(z.string()),
  retailOutlet: z.array(z.string()),
  creditCard: z.boolean(),
});

export type PaymentMethodsResponse = z.infer<typeof paymentMethodsResponseSchema>;

// Get invoice status schema
export const getInvoiceStatusSchema = z.object({
  invoiceId: z.string().min(1, "Invoice ID is required"),
});

// Get payment methods schema
export const getPaymentMethodsSchema = z.object({
  currency: currencyEnum,
});

// Payment history query schema
export const paymentHistoryQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val)).pipe(z.number().min(1)).optional().default("1"),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().min(1).max(100)).optional().default("10"),
  status: paymentStatusEnum.optional(),
});

export type PaymentHistoryQuery = z.infer<typeof paymentHistoryQuerySchema>;

// Payment history response schema
export const paymentHistoryResponseSchema = z.object({
  payments: z.array(z.object({
    id: z.string(),
    orderId: z.string(),
    orderNumber: z.string(),
    status: paymentStatusEnum,
    amount: z.number(),
    currency: currencyEnum,
    paymentMethod: z.string().nullable(),
    paidAt: z.string().nullable(),
    createdAt: z.string(),
  })),
  pagination: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
  }),
});

export type PaymentHistoryResponse = z.infer<typeof paymentHistoryResponseSchema>;
