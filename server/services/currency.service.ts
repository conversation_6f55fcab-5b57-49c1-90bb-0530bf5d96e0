import { prisma } from "../db";

// Exchange rates (should be fetched from real-time API in production)
const EXCHANGE_RATES = {
  USD_TO_IDR: 15000,
  IDR_TO_USD: 1 / 15000,
};

// Supported currencies
export type SupportedCurrency = 'USD' | 'IDR';

class CurrencyService {
  /**
   * Get current exchange rates
   */
  async getExchangeRates() {
    try {
      // In production, fetch from real-time API like:
      // - exchangerate-api.com
      // - fixer.io
      // - currencylayer.com
      
      // For now, return static rates
      return {
        success: true,
        data: {
          USD_TO_IDR: EXCHANGE_RATES.USD_TO_IDR,
          IDR_TO_USD: EXCHANGE_RATES.IDR_TO_USD,
          lastUpdated: new Date().toISOString(),
        }
      };
    } catch (error) {
      console.error('Failed to get exchange rates:', error);
      return {
        success: false,
        message: 'Failed to get exchange rates',
        data: EXCHANGE_RATES // Fallback to static rates
      };
    }
  }

  /**
   * Convert amount from one currency to another
   */
  convertCurrency(
    amount: number,
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): number {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      return amount * EXCHANGE_RATES.USD_TO_IDR;
    } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      return amount * EXCHANGE_RATES.IDR_TO_USD;
    }

    return amount;
  }

  /**
   * Get product prices in specified currency
   */
  async getProductPricesInCurrency(
    productIds: string[],
    currency: SupportedCurrency
  ) {
    try {
      const products = await prisma.product.findMany({
        where: {
          id: {
            in: productIds
          }
        },
        select: {
          id: true,
          priceUSD: true,
        }
      });

      const convertedPrices = products.map(product => {
        const basePrice = Number(product.priceUSD);
        const convertedPrice = this.convertCurrency(basePrice, 'USD', currency);
        
        return {
          id: product.id,
          originalPrice: basePrice,
          originalCurrency: 'USD' as const,
          convertedPrice: convertedPrice,
          currency: currency,
        };
      });

      return {
        success: true,
        data: convertedPrices
      };
    } catch (error) {
      console.error('Failed to get product prices:', error);
      return {
        success: false,
        message: 'Failed to get product prices'
      };
    }
  }

  /**
   * Get all products with prices in specified currency
   */
  async getAllProductsWithCurrency(
    currency: SupportedCurrency,
    page: number = 1,
    limit: number = 20,
    filters?: {
      category?: string;
      minPrice?: number;
      maxPrice?: number;
      search?: string;
    }
  ) {
    try {
      const offset = (page - 1) * limit;
      
      // Build where clause
      const where: any = {};
      
      if (filters?.category) {
        where.categoryId = filters.category;
      }
      
      if (filters?.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ];
      }

      // For price filtering, we need to convert the filter prices to USD first
      if (filters?.minPrice || filters?.maxPrice) {
        const priceFilter: any = {};
        
        if (filters.minPrice) {
          const minPriceUSD = this.convertCurrency(filters.minPrice, currency, 'USD');
          priceFilter.gte = minPriceUSD;
        }
        
        if (filters.maxPrice) {
          const maxPriceUSD = this.convertCurrency(filters.maxPrice, currency, 'USD');
          priceFilter.lte = maxPriceUSD;
        }
        
        where.priceUSD = priceFilter;
      }

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          include: {
            category: true,
            images: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              }
            },
            bids: {
              orderBy: {
                amount: 'desc'
              },
              take: 1,
              include: {
                bidder: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip: offset,
          take: limit,
        }),
        prisma.product.count({ where })
      ]);

      // Convert prices to requested currency
      const productsWithConvertedPrices = products.map(product => {
        const basePrice = Number(product.priceUSD);
        const convertedPrice = this.convertCurrency(basePrice, 'USD', currency);
        
        // Also convert bid amounts if any
        const convertedBids = product.bids.map(bid => ({
          ...bid,
          amount: this.convertCurrency(Number(bid.amount), 'USD', currency),
          originalAmount: Number(bid.amount),
          currency: currency,
        }));

        return {
          ...product,
          priceUSD: basePrice, // Keep original USD price
          price: convertedPrice, // Converted price
          currency: currency,
          bids: convertedBids,
        };
      });

      return {
        success: true,
        data: {
          products: productsWithConvertedPrices,
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
          },
          currency,
          exchangeRate: currency === 'USD' ? 1 : EXCHANGE_RATES.USD_TO_IDR,
        }
      };
    } catch (error) {
      console.error('Failed to get products with currency:', error);
      return {
        success: false,
        message: 'Failed to get products'
      };
    }
  }

  /**
   * Get single product with price in specified currency
   */
  async getProductWithCurrency(productId: string, currency: SupportedCurrency) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          category: true,
          images: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            }
          },
          bids: {
            orderBy: {
              amount: 'desc'
            },
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                }
              }
            }
          }
        }
      });

      if (!product) {
        return {
          success: false,
          message: 'Product not found'
        };
      }

      const basePrice = Number(product.priceUSD);
      const convertedPrice = this.convertCurrency(basePrice, 'USD', currency);
      
      // Convert bid amounts
      const convertedBids = product.bids.map(bid => ({
        ...bid,
        amount: this.convertCurrency(Number(bid.amount), 'USD', currency),
        originalAmount: Number(bid.amount),
        currency: currency,
      }));

      const productWithConvertedPrice = {
        ...product,
        priceUSD: basePrice,
        price: convertedPrice,
        currency: currency,
        bids: convertedBids,
      };

      return {
        success: true,
        data: productWithConvertedPrice
      };
    } catch (error) {
      console.error('Failed to get product with currency:', error);
      return {
        success: false,
        message: 'Failed to get product'
      };
    }
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: SupportedCurrency): string {
    const locale = currency === 'USD' ? 'en-US' : 'id-ID';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'IDR' ? 0 : 2,
      maximumFractionDigits: currency === 'IDR' ? 0 : 2,
    }).format(amount);
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies() {
    return [
      {
        code: 'USD',
        name: 'US Dollar',
        symbol: '$',
        locale: 'en-US',
      },
      {
        code: 'IDR',
        name: 'Indonesian Rupiah',
        symbol: 'Rp',
        locale: 'id-ID',
      }
    ];
  }
}

const currencyService = new CurrencyService();
export default currencyService;
