import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import {
  createInvoiceSchema,
  createEWalletChargeSchema,
  webhookPayloadSchema,
  invoiceResponseSchema,
  paymentStatusResponseSchema,
  paymentMethodsResponseSchema,
  getInvoiceStatusSchema,
  getPaymentMethodsSchema,
  paymentHistoryQuerySchema,
  paymentHistoryResponseSchema,
} from "../schemas/payment.schema";
import paymentController from "../controllers/payment.controller";
import { authMiddleware } from "../middlewares/auth";

const paymentRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Create Invoice Route
const createInvoiceRoute = createRoute({
  method: "post",
  path: "/invoice",
  middleware: [authMiddleware] as const,
  request: {
    body: {
      content: {
        "application/json": {
          schema: createInvoiceSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              id: z.string(),
              externalId: z.string(),
              status: z.string(),
              amount: z.number(),
              currency: z.string(),
              invoiceUrl: z.string(),
              expiryDate: z.string(),
            }),
          }),
        },
      },
      description: "Invoice created successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Bad request",
    },
    401: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Unauthorized",
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Order not found",
    },
    500: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Internal server error",
    },
  },
  tags: ["Payment"],
});

// Create eWallet Charge Route
const createEWalletChargeRoute = createRoute({
  method: "post",
  path: "/ewallet",
  middleware: [authMiddleware] as const,
  request: {
    body: {
      content: {
        "application/json": {
          schema: createEWalletChargeSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        },
      },
      description: "eWallet charge created successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Bad request",
    },
    401: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Unauthorized",
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Order not found",
    },
    500: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Internal server error",
    },
  },
  tags: ["Payment"],
});

// Get Invoice Status Route
const getInvoiceStatusRoute = createRoute({
  method: "get",
  path: "/invoice/{invoiceId}/status",
  middleware: [authMiddleware] as const,
  request: {
    params: z.object({
      invoiceId: z.string().min(1, "Invoice ID is required"),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        },
      },
      description: "Invoice status retrieved successfully",
    },
    401: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Unauthorized",
    },
    403: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Access denied",
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Payment not found",
    },
    500: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Internal server error",
    },
  },
  tags: ["Payment"],
});

// Get Payment Status Route
const getPaymentStatusRoute = createRoute({
  method: "get",
  path: "/order/{orderId}/status",
  middleware: [authMiddleware] as const,
  request: {
    params: z.object({
      orderId: z.string().uuid("Invalid order ID"),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: paymentStatusResponseSchema,
          }),
        },
      },
      description: "Payment status retrieved successfully",
    },
    401: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Unauthorized",
    },
    403: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Access denied",
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Payment not found",
    },
    500: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Internal server error",
    },
  },
  tags: ["Payment"],
});

// Webhook Route (no auth required)
const webhookRoute = createRoute({
  method: "post",
  path: "/webhook",
  request: {
    body: {
      content: {
        "application/json": {
          schema: webhookPayloadSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Webhook processed successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Invalid webhook payload",
    },
    500: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Internal server error",
    },
  },
  tags: ["Payment"],
});

// Get Payment Methods Route
const getPaymentMethodsRoute = createRoute({
  method: "get",
  path: "/methods",
  middleware: [authMiddleware] as const,
  request: {
    query: z.object({
      currency: z.enum(["IDR", "USD"]),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: paymentMethodsResponseSchema,
          }),
        },
      },
      description: "Payment methods retrieved successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Invalid currency",
    },
    401: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Unauthorized",
    },
    500: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Internal server error",
    },
  },
  tags: ["Payment"],
});

// Get Payment History Route
const getPaymentHistoryRoute = createRoute({
  method: "get",
  path: "/history",
  middleware: [authMiddleware] as const,
  request: {
    query: paymentHistoryQuerySchema,
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: paymentHistoryResponseSchema,
          }),
        },
      },
      description: "Payment history retrieved successfully",
    },
    401: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Unauthorized",
    },
    500: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Internal server error",
    },
  },
  tags: ["Payment"],
});

// Register routes with simpler approach for now
paymentRoutes.post('/invoice', authMiddleware, paymentController.createInvoice);
paymentRoutes.post('/ewallet', authMiddleware, paymentController.createEWalletCharge);
paymentRoutes.post('/virtual-account', authMiddleware, paymentController.createVirtualAccount);
paymentRoutes.post('/retail-outlet', authMiddleware, paymentController.createRetailOutlet);
paymentRoutes.post('/qr-code', authMiddleware, paymentController.createQRCode);
paymentRoutes.get('/invoice/:invoiceId/status', authMiddleware, paymentController.getInvoiceStatus);
paymentRoutes.get('/order/:orderId/status', authMiddleware, paymentController.getPaymentStatus);
paymentRoutes.get('/:paymentId', authMiddleware, paymentController.getPaymentDetails);
paymentRoutes.post('/webhook', paymentController.handleWebhook);
paymentRoutes.get('/methods', authMiddleware, paymentController.getPaymentMethods);
paymentRoutes.get('/history', authMiddleware, paymentController.getPaymentHistory);

export { paymentRoutes };
