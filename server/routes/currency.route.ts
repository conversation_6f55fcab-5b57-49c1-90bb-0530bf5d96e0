import { Hono } from "hono";
import currencyController from "../controllers/currency.controller";

const currencyRoutes = new Hono();

// Get exchange rates
currencyRoutes.get('/rates', currencyController.getExchangeRates);

// Get supported currencies
currencyRoutes.get('/supported', currencyController.getSupportedCurrencies);

// Convert currency
currencyRoutes.post('/convert', currencyController.convertCurrency);

// Get products with currency conversion
currencyRoutes.get('/products', currencyController.getProductsWithCurrency);

// Get single product with currency conversion
currencyRoutes.get('/products/:productId', currencyController.getProductWithCurrency);

// Get product prices in specific currency
currencyRoutes.post('/prices', currencyController.getProductPrices);

// Format currency amount
currencyRoutes.get('/format', currencyController.formatCurrency);

export { currencyRoutes };
