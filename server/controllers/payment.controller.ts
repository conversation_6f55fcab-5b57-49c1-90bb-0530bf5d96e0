import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import paymentService from "../services/payment.service";
import { prisma } from "../db";
import { CreateInvoiceInput, CreateEWalletChargeInput } from "../schemas/payment.schema";

class PaymentController {
  /**
   * Create Xendit invoice for payment
   */
  async createInvoice(c: Context) {
    try {
      const body = await c.req.json() as CreateInvoiceInput;
      
      // Verify order exists and belongs to user
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const order = await prisma.order.findFirst({
        where: {
          id: body.orderId,
          userId: user.id,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId: body.orderId },
      });

      if (existingPayment && existingPayment.status === 'PAID') {
        return c.json(errorResponse("Order already paid"), 400);
      }

      // Create invoice
      const result = await paymentService.createInvoice(body);

      if (!result.status || !result.data) {
        return c.json(errorResponse(result.message || "Failed to create invoice"), 400);
      }

      return c.json({
        status: true,
        message: "Invoice created successfully",
        data: result.data
      }, 201);
    } catch (error: any) {
      console.error("Create invoice error:", error);
      return c.json(errorResponse(error.message || "Failed to create invoice"), 500);
    }
  }

  /**
   * Create eWallet charge
   */
  async createEWalletCharge(c: Context) {
    try {
      const body = await c.req.json() as CreateEWalletChargeInput;
      
      // Verify order exists and belongs to user
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const order = await prisma.order.findFirst({
        where: {
          id: body.orderId,
          userId: user.id,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      // Create eWallet charge
      const result = await paymentService.createEWalletCharge(body);

      if (!result.status || !result.data) {
        return c.json(errorResponse(result.message || "Failed to create eWallet charge"), 400);
      }

      return c.json({
        status: true,
        message: "eWallet charge created successfully",
        data: result.data
      }, 201);
    } catch (error: any) {
      console.error("Create eWallet charge error:", error);
      return c.json(errorResponse(error.message || "Failed to create eWallet charge"), 500);
    }
  }

  /**
   * Get invoice status from Xendit
   */
  async getInvoiceStatus(c: Context) {
    try {
      const invoiceId = c.req.param('invoiceId');
      
      if (!invoiceId) {
        return c.json(errorResponse("Invoice ID is required"), 400);
      }

      // Find payment by Xendit invoice ID
      const payment = await prisma.payment.findUnique({
        where: { xenditInvoiceId: invoiceId },
        include: { order: true },
      });

      if (!payment) {
        return c.json(errorResponse("Payment not found"), 404);
      }

      // Verify user access
      const user = c.get('user');
      if (!user || payment.order.userId !== user.id) {
        return c.json(errorResponse("Access denied"), 403);
      }

      return c.json({
        status: true,
        message: "Invoice status retrieved",
        data: {
          id: payment.id,
          status: payment.status,
          amount: Number(payment.amount),
          currency: payment.currency,
          paidAt: payment.paidAt?.toISOString() || null,
          paymentMethod: payment.paymentMethod,
          paymentChannel: payment.paymentChannel,
          invoiceUrl: payment.invoiceUrl,
        }
      });
    } catch (error: any) {
      console.error("Get invoice status error:", error);
      return c.json(errorResponse(error.message || "Failed to get invoice status"), 500);
    }
  }

  /**
   * Get payment status for an order
   */
  async getPaymentStatus(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      
      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      // Find payment by order ID
      const payment = await prisma.payment.findFirst({
        where: { orderId },
        include: { order: true },
        orderBy: { createdAt: 'desc' },
      });

      if (!payment) {
        return c.json(errorResponse("Payment not found"), 404);
      }

      // Verify user access
      const user = c.get('user');
      if (!user || payment.order.userId !== user.id) {
        return c.json(errorResponse("Access denied"), 403);
      }

      return c.json({
        status: true,
        message: "Payment status retrieved",
        data: {
          id: payment.id,
          orderId: payment.orderId,
          status: payment.status,
          amount: Number(payment.amount),
          currency: payment.currency,
          paidAt: payment.paidAt?.toISOString() || null,
          paymentMethod: payment.paymentMethod,
          paymentChannel: payment.paymentChannel,
          createdAt: payment.createdAt.toISOString(),
          updatedAt: payment.updatedAt.toISOString(),
        }
      });
    } catch (error: any) {
      console.error("Get payment status error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment status"), 500);
    }
  }

  /**
   * Handle Xendit webhook
   */
  async handleWebhook(c: Context) {
    try {
      const signature = c.req.header('x-callback-token') || '';
      const payload = await c.req.json();

      console.log('Webhook received:', { payload, signature });

      const result = await paymentService.handleWebhook(payload, signature);

      return c.json({
        status: true,
        message: "Webhook processed successfully",
        data: result
      });
    } catch (error: any) {
      console.error("Webhook handling error:", error);
      return c.json(errorResponse(error.message || "Failed to process webhook"), 500);
    }
  }

  /**
   * Get supported payment methods
   */
  async getPaymentMethods(c: Context) {
    try {
      const { currency } = c.req.query();

      if (!currency || !['IDR', 'USD'].includes(currency)) {
        return c.json(errorResponse("Valid currency (IDR or USD) is required"), 400);
      }

      const methods = paymentService.getPaymentMethods(currency as 'IDR' | 'USD');

      return c.json({
        status: true,
        message: "Payment methods retrieved",
        data: methods
      });
    } catch (error: any) {
      console.error("Get payment methods error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment methods"), 500);
    }
  }

  /**
   * Get user's payment history
   */
  async getPaymentHistory(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const { page = "1", limit = "10", status } = c.req.query();

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      const where: any = {
        order: {
          userId: user.id,
        },
      };

      if (status) {
        where.status = status;
      }

      const [payments, total] = await Promise.all([
        prisma.payment.findMany({
          where,
          include: {
            order: {
              select: {
                orderNumber: true,
                total: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limitNum,
        }),
        prisma.payment.count({ where }),
      ]);

      const formattedPayments = payments.map(payment => ({
        id: payment.id,
        orderId: payment.orderId,
        orderNumber: payment.order.orderNumber,
        status: payment.status,
        amount: Number(payment.amount),
        currency: payment.currency,
        paymentMethod: payment.paymentMethod,
        paidAt: payment.paidAt?.toISOString() || null,
        createdAt: payment.createdAt.toISOString(),
      }));

      return c.json({
        status: true,
        message: "Payment history retrieved",
        data: {
          payments: formattedPayments,
          pagination: {
            total,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(total / limitNum),
          },
        }
      });
    } catch (error: any) {
      console.error("Get payment history error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment history"), 500);
    }
  }
}

const paymentController = new PaymentController();
export default paymentController;
